import Anthropic from '@anthropic-ai/sdk';
import { <PERSON><PERSON> } from 'winston';
import { <PERSON><PERSON><PERSON>ider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class AnthropicProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: LL<PERSON>rovider['type'] = 'anthropic';
  
  private client: Anthropic;
  private config: <PERSON><PERSON><PERSON><PERSON>;
  private logger: Logger;

  constructor(config: <PERSON><PERSON><PERSON><PERSON>, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    if (!config.apiKey) {
      throw new ProviderError('Anthropic API key is required', config.name);
    }

    this.client = new Anthropic({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Test with a minimal request
      await this.client.messages.create({
        model: this.config.model,
        max_tokens: 1,
        messages: [{ role: 'user', content: 'test' }],
      });
      return true;
    } catch (error) {
      this.logger.warn(`Anthropic provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const { systemMessage, userMessages } = this.convertMessages(messages);
      
      const response = await this.client.messages.create({
        model: this.config.model,
        max_tokens: options.maxTokens ?? this.config.maxTokens,
        temperature: options.temperature ?? this.config.temperature,
        top_p: options.topP,
        stop_sequences: options.stop,
        system: systemMessage,
        messages: userMessages,
        tools: this.getToolDefinitions(),
      });

      let content = '';
      const toolCalls: any[] = [];

      for (const block of response.content) {
        if (block.type === 'text') {
          content += block.text;
        } else if (block.type === 'tool_use') {
          toolCalls.push({
            id: block.id,
            type: 'function',
            function: {
              name: block.name,
              arguments: JSON.stringify(block.input),
            },
          });
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        usage: response.usage ? {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
        } : undefined,
        finishReason: this.mapFinishReason(response.stop_reason),
      };
    } catch (error) {
      if (error instanceof Anthropic.APIError) {
        throw new ProviderError(
          `Anthropic API error: ${error.message}`,
          this.name,
          { status: error.status }
        );
      }
      throw new ProviderError(`Anthropic request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): {
    systemMessage?: string;
    userMessages: Anthropic.Messages.MessageParam[];
  } {
    let systemMessage: string | undefined;
    const userMessages: Anthropic.Messages.MessageParam[] = [];

    for (const msg of messages) {
      switch (msg.role) {
        case 'system':
          systemMessage = msg.content;
          break;
        case 'user':
          userMessages.push({ role: 'user', content: msg.content });
          break;
        case 'assistant':
          if (msg.toolCalls) {
            const content: any[] = [];
            if (msg.content) {
              content.push({ type: 'text', text: msg.content });
            }
            for (const call of msg.toolCalls) {
              content.push({
                type: 'tool_use',
                id: call.id,
                name: call.function.name,
                input: JSON.parse(call.function.arguments),
              });
            }
            userMessages.push({ role: 'assistant', content });
          } else {
            userMessages.push({ role: 'assistant', content: msg.content });
          }
          break;
        case 'tool':
          userMessages.push({
            role: 'user',
            content: [
              {
                type: 'tool_result',
                tool_use_id: msg.toolCallId!,
                content: msg.content,
              },
            ],
          });
          break;
        default:
          throw new ProviderError(`Unsupported message role: ${msg.role}`, this.name);
      }
    }

    return { systemMessage, userMessages };
  }

  private getToolDefinitions(): Anthropic.Messages.Tool[] {
    return [
      {
        name: 'execute_shell_command',
        description: 'Execute shell commands with full system access',
        input_schema: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: 'The shell command to execute',
            },
            workingDirectory: {
              type: 'string',
              description: 'Working directory for command execution',
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds',
            },
          },
          required: ['command'],
        },
      },
      {
        name: 'read_file',
        description: 'Read file contents',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'File path to read',
            },
            encoding: {
              type: 'string',
              description: 'File encoding (default: utf8)',
            },
          },
          required: ['path'],
        },
      },
      {
        name: 'write_file',
        description: 'Write content to file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'File path to write',
            },
            content: {
              type: 'string',
              description: 'Content to write',
            },
            encoding: {
              type: 'string',
              description: 'File encoding (default: utf8)',
            },
            createDirectories: {
              type: 'boolean',
              description: 'Create parent directories if they don\'t exist',
            },
          },
          required: ['path', 'content'],
        },
      },
      {
        name: 'search_files',
        description: 'Search for files using glob patterns',
        input_schema: {
          type: 'object',
          properties: {
            pattern: {
              type: 'string',
              description: 'Glob pattern to search for files',
            },
            directory: {
              type: 'string',
              description: 'Directory to search in',
            },
            includeContent: {
              type: 'boolean',
              description: 'Include file content in results',
            },
          },
          required: ['pattern'],
        },
      },
      {
        name: 'analyze_project',
        description: 'Analyze project structure and dependencies',
        input_schema: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: 'Project directory to analyze',
            },
            includeGitInfo: {
              type: 'boolean',
              description: 'Include git repository information',
            },
          },
          required: ['directory'],
        },
      },
    ];
  }

  private mapFinishReason(reason: string | null): LLMResponse['finishReason'] {
    switch (reason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'tool_use':
        return 'tool_calls';
      case 'stop_sequence':
        return 'stop';
      default:
        return 'stop';
    }
  }

  estimateTokens(text: string): number {
    // Rough estimation for Claude models: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    // Model-specific max tokens for Claude
    const modelLimits: Record<string, number> = {
      'claude-3-opus-20240229': 4096,
      'claude-3-sonnet-20240229': 4096,
      'claude-3-haiku-20240307': 4096,
      'claude-2.1': 4096,
      'claude-2.0': 4096,
      'claude-instant-1.2': 4096,
    };

    return modelLimits[this.config.model] || this.config.maxTokens;
  }
}
