import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { nanoid } from 'nanoid';
import { 
  AgentContext, 
  ProjectStructure, 
  EnvironmentInfo, 
  FileInfo, 
  DirectoryInfo,
  GitInfo,
  PackageInfo,
  ContextMemory,
  AgentAction 
} from '@/types';
import { ProjectAnalyzer } from './project-analyzer';
import { EnvironmentDetector } from './environment-detector';
import { MemoryManager } from './memory-manager';

export class ContextManager extends EventEmitter {
  private currentContext?: AgentContext;
  private readonly logger: Logger;
  private readonly projectAnalyzer: ProjectAnalyzer;
  private readonly environmentDetector: EnvironmentDetector;
  private readonly contextCache = new Map<string, AgentContext>();
  private memoryManager?: MemoryManager;
  private autoSaveEnabled = true;
  private saveInterval?: NodeJS.Timeout;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.projectAnalyzer = new ProjectAnalyzer(logger);
    this.environmentDetector = new EnvironmentDetector(logger);
  }

  async initializeContext(workingDirectory: string, sessionId?: string): Promise<AgentContext> {
    this.logger.info('Initializing context', { workingDirectory, sessionId });

    const id = sessionId || nanoid();
    
    try {
      // Analyze project structure
      const projectStructure = await this.projectAnalyzer.analyzeProject(workingDirectory);
      
      // Detect environment
      const environment = await this.environmentDetector.detectEnvironment(workingDirectory);
      
      // Create context
      const context: AgentContext = {
        sessionId: id,
        workingDirectory,
        projectStructure,
        environment,
        history: [],
        memory: {
          facts: {},
          patterns: [],
          preferences: {},
          learnings: [],
        },
      };

      this.currentContext = context;
      this.contextCache.set(id, context);

      // Initialize memory manager for this session
      this.memoryManager = new MemoryManager(this.logger, id);

      // Start auto-save if enabled
      if (this.autoSaveEnabled) {
        this.startAutoSave();
      }

      this.emit('contextInitialized', { context });
      this.logger.info('Context initialized successfully', { sessionId: id });

      return context;
    } catch (error) {
      this.logger.error('Failed to initialize context', { error, workingDirectory });
      throw error;
    }
  }

  async updateContext(updates: Partial<AgentContext>): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to update');
    }

    Object.assign(this.currentContext, updates);
    this.contextCache.set(this.currentContext.sessionId, this.currentContext);
    
    this.emit('contextUpdated', { context: this.currentContext });
    this.logger.debug('Context updated', { sessionId: this.currentContext.sessionId });
  }

  async refreshProjectStructure(): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to refresh');
    }

    this.logger.info('Refreshing project structure');
    
    try {
      const newStructure = await this.projectAnalyzer.analyzeProject(
        this.currentContext.workingDirectory
      );
      
      await this.updateContext({ projectStructure: newStructure });
      this.emit('projectStructureRefreshed', { structure: newStructure });
    } catch (error) {
      this.logger.error('Failed to refresh project structure', { error });
      throw error;
    }
  }

  async addAction(action: AgentAction): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to add action to');
    }

    this.currentContext.history.push(action);
    
    // Limit history size to prevent memory issues
    const maxHistorySize = 1000;
    if (this.currentContext.history.length > maxHistorySize) {
      this.currentContext.history = this.currentContext.history.slice(-maxHistorySize);
    }

    // Learn from successful actions
    if (action.success) {
      await this.learnFromAction(action);
    }

    this.emit('actionAdded', { action });
  }

  async addMemory(key: string, value: unknown, context?: string): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to add memory to');
    }

    this.currentContext.memory.facts[key] = value;

    if (this.memoryManager) {
      await this.memoryManager.addFact(key, value, context);
    }

    this.emit('memoryAdded', { key, value, context });
  }

  async addLearning(learning: string, context?: string): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to add learning to');
    }

    this.currentContext.memory.learnings.push(learning);

    // Limit learnings to prevent memory bloat
    const maxLearnings = 100;
    if (this.currentContext.memory.learnings.length > maxLearnings) {
      this.currentContext.memory.learnings = this.currentContext.memory.learnings.slice(-maxLearnings);
    }

    if (this.memoryManager) {
      await this.memoryManager.addLearning(learning, context);
    }

    this.emit('learningAdded', { learning, context });
  }

  async addPattern(pattern: string): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to add pattern to');
    }

    if (!this.currentContext.memory.patterns.includes(pattern)) {
      this.currentContext.memory.patterns.push(pattern);
      this.emit('patternAdded', { pattern });
    }
  }

  private async learnFromAction(action: AgentAction): Promise<void> {
    try {
      // Extract patterns from successful actions
      const actionType = action.type;
      const duration = action.duration;
      
      // Learn about performance
      if (duration < 1000) {
        await this.addPattern(`${actionType}_fast_execution`);
      } else if (duration > 10000) {
        await this.addPattern(`${actionType}_slow_execution`);
      }

      // Learn about tool usage
      if (typeof action.input === 'object' && action.input !== null) {
        const input = action.input as any;
        if (input.toolName) {
          await this.addPattern(`successful_${input.toolName}_usage`);
        }
      }

      // Add general learning
      await this.addLearning(`Successfully executed ${actionType} in ${duration}ms`);
    } catch (error) {
      this.logger.warn('Failed to learn from action', { error, actionId: action.id });
    }
  }

  async saveContext(filePath?: string): Promise<void> {
    if (!this.currentContext) {
      throw new Error('No active context to save');
    }

    const savePath = filePath || this.getDefaultSavePath();
    
    try {
      await fs.mkdir(path.dirname(savePath), { recursive: true });
      await fs.writeFile(savePath, JSON.stringify(this.currentContext, null, 2));
      
      this.logger.info('Context saved', { path: savePath });
      this.emit('contextSaved', { path: savePath });
    } catch (error) {
      this.logger.error('Failed to save context', { error, path: savePath });
      throw error;
    }
  }

  async loadContext(filePath: string): Promise<AgentContext> {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      const context = JSON.parse(data) as AgentContext;
      
      // Validate and restore context
      this.currentContext = context;
      this.contextCache.set(context.sessionId, context);
      
      this.logger.info('Context loaded', { sessionId: context.sessionId, path: filePath });
      this.emit('contextLoaded', { context, path: filePath });
      
      return context;
    } catch (error) {
      this.logger.error('Failed to load context', { error, path: filePath });
      throw error;
    }
  }

  getCurrentContext(): AgentContext | undefined {
    return this.currentContext;
  }

  getContextHistory(): AgentAction[] {
    return this.currentContext?.history || [];
  }

  getMemory(): ContextMemory | undefined {
    return this.currentContext?.memory;
  }

  getMemoryManager(): MemoryManager | undefined {
    return this.memoryManager;
  }

  async getMemoryInsights(context?: string): Promise<any[]> {
    if (!this.memoryManager) {
      return [];
    }

    return context
      ? this.memoryManager.getRelevantInsights(context)
      : this.memoryManager.getInsights();
  }

  async getMemoryPatterns(): Promise<any[]> {
    if (!this.memoryManager) {
      return [];
    }

    return this.memoryManager.getPatterns();
  }

  private getDefaultSavePath(): string {
    const sessionId = this.currentContext?.sessionId || 'unknown';
    return path.join(process.cwd(), '.agent-context', `${sessionId}.json`);
  }

  private startAutoSave(): void {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    this.saveInterval = setInterval(async () => {
      try {
        await this.saveContext();
      } catch (error) {
        this.logger.warn('Auto-save failed', { error });
      }
    }, 30000); // Save every 30 seconds
  }

  setAutoSave(enabled: boolean): void {
    this.autoSaveEnabled = enabled;
    
    if (enabled && this.currentContext) {
      this.startAutoSave();
    } else if (this.saveInterval) {
      clearInterval(this.saveInterval);
      this.saveInterval = undefined;
    }
  }

  async shutdown(): Promise<void> {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    if (this.currentContext && this.autoSaveEnabled) {
      try {
        await this.saveContext();
      } catch (error) {
        this.logger.warn('Failed to save context during shutdown', { error });
      }
    }

    if (this.memoryManager) {
      try {
        await this.memoryManager.shutdown();
      } catch (error) {
        this.logger.warn('Failed to shutdown memory manager', { error });
      }
    }

    this.contextCache.clear();
    this.currentContext = undefined;
    this.removeAllListeners();
    this.logger.info('Context manager shutdown');
  }
}
