import { exec } from 'child_process';
import { promisify } from 'util';
import { Logger } from 'winston';
import { EnvironmentInfo } from '@/types';

const execAsync = promisify(exec);

export class EnvironmentDetector {
  private readonly logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async detectEnvironment(workingDirectory: string): Promise<EnvironmentInfo> {
    this.logger.info('Detecting environment', { workingDirectory });

    try {
      const [
        nodeVersion,
        npmVersion,
        pythonVersion,
        gitVersion,
        shell,
        env
      ] = await Promise.allSettled([
        this.getNodeVersion(),
        this.getNpmVersion(),
        this.getPythonVersion(),
        this.getGitVersion(),
        this.getShell(),
        this.getEnvironmentVariables(),
      ]);

      const environment: EnvironmentInfo = {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: nodeVersion.status === 'fulfilled' ? nodeVersion.value : 'unknown',
        npmVersion: npmVersion.status === 'fulfilled' ? npmVersion.value : 'unknown',
        pythonVersion: pythonVersion.status === 'fulfilled' ? pythonVersion.value : 'unknown',
        gitVersion: gitVersion.status === 'fulfilled' ? gitVersion.value : 'unknown',
        shell: shell.status === 'fulfilled' ? shell.value : 'unknown',
        cwd: workingDirectory,
        env: env.status === 'fulfilled' ? env.value : {},
      };

      this.logger.info('Environment detection complete', {
        platform: environment.platform,
        nodeVersion: environment.nodeVersion,
        shell: environment.shell,
      });

      return environment;
    } catch (error) {
      this.logger.error('Environment detection failed', { error });
      throw error;
    }
  }

  private async getNodeVersion(): Promise<string> {
    try {
      const { stdout } = await execAsync('node --version');
      return stdout.trim();
    } catch {
      return process.version;
    }
  }

  private async getNpmVersion(): Promise<string | undefined> {
    try {
      const { stdout } = await execAsync('npm --version');
      return stdout.trim();
    } catch {
      return undefined;
    }
  }

  private async getPythonVersion(): Promise<string | undefined> {
    try {
      // Try python3 first, then python
      let stdout: string;
      try {
        const result = await execAsync('python3 --version');
        stdout = result.stdout;
      } catch {
        const result = await execAsync('python --version');
        stdout = result.stdout;
      }
      return stdout.trim();
    } catch {
      return undefined;
    }
  }

  private async getGitVersion(): Promise<string | undefined> {
    try {
      const { stdout } = await execAsync('git --version');
      return stdout.trim();
    } catch {
      return undefined;
    }
  }

  private async getShell(): Promise<string> {
    try {
      if (process.platform === 'win32') {
        // On Windows, check if we're in WSL
        try {
          await execAsync('wsl --version');
          return 'wsl';
        } catch {
          return process.env['SHELL'] || 'cmd';
        }
      } else {
        return process.env['SHELL'] || 'bash';
      }
    } catch {
      return 'unknown';
    }
  }

  private async getEnvironmentVariables(): Promise<Record<string, string>> {
    const relevantVars = [
      'PATH',
      'HOME',
      'USER',
      'USERNAME',
      'SHELL',
      'TERM',
      'NODE_ENV',
      'NPM_CONFIG_PREFIX',
      'PYTHON_PATH',
      'VIRTUAL_ENV',
      'CONDA_DEFAULT_ENV',
      'JAVA_HOME',
      'GOPATH',
      'CARGO_HOME',
      'RUSTUP_HOME',
    ];

    const env: Record<string, string> = {};
    
    for (const varName of relevantVars) {
      const value = process.env[varName];
      if (value) {
        env[varName] = value;
      }
    }

    return env;
  }

  async detectRuntimeVersions(): Promise<Record<string, string>> {
    const versions: Record<string, string> = {};

    const commands = [
      { name: 'node', command: 'node --version' },
      { name: 'npm', command: 'npm --version' },
      { name: 'yarn', command: 'yarn --version' },
      { name: 'pnpm', command: 'pnpm --version' },
      { name: 'python', command: 'python --version' },
      { name: 'python3', command: 'python3 --version' },
      { name: 'pip', command: 'pip --version' },
      { name: 'pip3', command: 'pip3 --version' },
      { name: 'poetry', command: 'poetry --version' },
      { name: 'conda', command: 'conda --version' },
      { name: 'git', command: 'git --version' },
      { name: 'docker', command: 'docker --version' },
      { name: 'docker-compose', command: 'docker-compose --version' },
      { name: 'kubectl', command: 'kubectl version --client' },
      { name: 'java', command: 'java -version' },
      { name: 'javac', command: 'javac -version' },
      { name: 'mvn', command: 'mvn --version' },
      { name: 'gradle', command: 'gradle --version' },
      { name: 'go', command: 'go version' },
      { name: 'rust', command: 'rustc --version' },
      { name: 'cargo', command: 'cargo --version' },
      { name: 'php', command: 'php --version' },
      { name: 'composer', command: 'composer --version' },
      { name: 'ruby', command: 'ruby --version' },
      { name: 'gem', command: 'gem --version' },
      { name: 'bundle', command: 'bundle --version' },
    ];

    const results = await Promise.allSettled(
      commands.map(async ({ name, command }) => {
        try {
          const { stdout, stderr } = await execAsync(command);
          const output = stdout || stderr;
          return { name, version: output.split('\n')[0]?.trim() || 'unknown' };
        } catch {
          return { name, version: 'not installed' };
        }
      })
    );

    results.forEach(result => {
      if (result.status === 'fulfilled') {
        versions[result.value.name] = result.value.version;
      }
    });

    return versions;
  }

  async detectSystemInfo(): Promise<Record<string, unknown>> {
    const info: Record<string, unknown> = {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    };

    try {
      // Get system information
      if (process.platform === 'linux') {
        try {
          const { stdout } = await execAsync('cat /proc/version');
          info.kernelVersion = stdout.trim();
        } catch {
          // Ignore
        }

        try {
          const { stdout } = await execAsync('lsb_release -d');
          info.distribution = stdout.replace('Description:', '').trim();
        } catch {
          // Ignore
        }
      } else if (process.platform === 'darwin') {
        try {
          const { stdout } = await execAsync('sw_vers -productVersion');
          info.macosVersion = stdout.trim();
        } catch {
          // Ignore
        }
      } else if (process.platform === 'win32') {
        try {
          const { stdout } = await execAsync('ver');
          info.windowsVersion = stdout.trim();
        } catch {
          // Ignore
        }
      }

      // Get CPU info
      try {
        const { stdout } = await execAsync('nproc');
        info.cpuCores = parseInt(stdout.trim(), 10);
      } catch {
        info.cpuCores = require('os').cpus().length;
      }

      // Get memory info
      try {
        if (process.platform === 'linux') {
          const { stdout } = await execAsync('free -m');
          const lines = stdout.split('\n');
          const memLine = lines.find(line => line.startsWith('Mem:'));
          if (memLine) {
            const parts = memLine.split(/\s+/);
            info.totalMemoryMB = parseInt(parts[1] || '0', 10);
            info.availableMemoryMB = parseInt(parts[6] || '0', 10);
          }
        }
      } catch {
        // Ignore
      }

    } catch (error) {
      this.logger.warn('Failed to detect some system information', { error });
    }

    return info;
  }
}
