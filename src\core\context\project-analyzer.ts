import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { exec } from 'child_process';
import { promisify } from 'util';
import { 
  ProjectStructure, 
  FileInfo, 
  DirectoryInfo, 
  GitInfo, 
  PackageInfo 
} from '@/types';

const execAsync = promisify(exec);

export class ProjectAnalyzer {
  private readonly logger: Logger;
  private readonly ignoredDirs = new Set([
    'node_modules',
    '.git',
    '.vscode',
    '.idea',
    'dist',
    'build',
    'coverage',
    '.nyc_output',
    'target',
    '__pycache__',
    '.pytest_cache',
    'venv',
    'env',
    '.env',
  ]);

  private readonly ignoredFiles = new Set([
    '.DS_Store',
    'Thumbs.db',
    '.gitignore',
    '.npmignore',
    '.eslintcache',
  ]);

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async analyzeProject(rootPath: string): Promise<ProjectStructure> {
    this.logger.info('Analyzing project structure', { rootPath });

    try {
      const [files, directories, gitInfo, packageInfo] = await Promise.all([
        this.scanFiles(rootPath),
        this.scanDirectories(rootPath),
        this.getGitInfo(rootPath),
        this.getPackageInfo(rootPath),
      ]);

      const structure: ProjectStructure = {
        root: rootPath,
        files,
        directories,
        gitInfo,
        packageInfo,
      };

      this.logger.info('Project analysis complete', {
        fileCount: files.length,
        directoryCount: directories.length,
        hasGit: !!gitInfo,
        hasPackage: !!packageInfo,
      });

      return structure;
    } catch (error) {
      this.logger.error('Project analysis failed', { error, rootPath });
      throw error;
    }
  }

  private async scanFiles(rootPath: string, currentPath = ''): Promise<FileInfo[]> {
    const files: FileInfo[] = [];
    const fullPath = path.join(rootPath, currentPath);

    try {
      const entries = await fs.readdir(fullPath, { withFileTypes: true });

      for (const entry of entries) {
        const entryPath = path.join(currentPath, entry.name);
        const fullEntryPath = path.join(rootPath, entryPath);

        if (entry.isFile() && !this.ignoredFiles.has(entry.name)) {
          try {
            const stats = await fs.stat(fullEntryPath);
            const fileInfo: FileInfo = {
              path: entryPath,
              name: entry.name,
              extension: path.extname(entry.name),
              size: stats.size,
              lastModified: stats.mtime,
              type: 'file',
              permissions: this.getPermissions(stats.mode),
            };

            // Include content for small text files
            if (this.isTextFile(entry.name) && stats.size < 10000) {
              try {
                fileInfo.content = await fs.readFile(fullEntryPath, 'utf8');
              } catch {
                // Ignore read errors for content
              }
            }

            files.push(fileInfo);
          } catch (error) {
            this.logger.warn('Failed to analyze file', { path: entryPath, error });
          }
        } else if (entry.isDirectory() && !this.ignoredDirs.has(entry.name)) {
          // Recursively scan subdirectories
          const subFiles = await this.scanFiles(rootPath, entryPath);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      this.logger.warn('Failed to scan directory', { path: currentPath, error });
    }

    return files;
  }

  private async scanDirectories(rootPath: string, currentPath = ''): Promise<DirectoryInfo[]> {
    const directories: DirectoryInfo[] = [];
    const fullPath = path.join(rootPath, currentPath);

    try {
      const entries = await fs.readdir(fullPath, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory() && !this.ignoredDirs.has(entry.name)) {
          const entryPath = path.join(currentPath, entry.name);
          const fullEntryPath = path.join(rootPath, entryPath);

          try {
            const stats = await fs.stat(fullEntryPath);
            const children = await fs.readdir(fullEntryPath);

            const dirInfo: DirectoryInfo = {
              path: entryPath,
              name: entry.name,
              children: children.filter(child => !this.ignoredFiles.has(child)),
              size: children.length,
              lastModified: stats.mtime,
            };

            directories.push(dirInfo);

            // Recursively scan subdirectories
            const subDirs = await this.scanDirectories(rootPath, entryPath);
            directories.push(...subDirs);
          } catch (error) {
            this.logger.warn('Failed to analyze directory', { path: entryPath, error });
          }
        }
      }
    } catch (error) {
      this.logger.warn('Failed to scan directories', { path: currentPath, error });
    }

    return directories;
  }

  private async getGitInfo(rootPath: string): Promise<GitInfo | undefined> {
    try {
      const gitDir = path.join(rootPath, '.git');
      await fs.access(gitDir);

      const [branchResult, commitResult, remoteResult, statusResult] = await Promise.allSettled([
        execAsync('git rev-parse --abbrev-ref HEAD', { cwd: rootPath }),
        execAsync('git rev-parse HEAD', { cwd: rootPath }),
        execAsync('git remote get-url origin', { cwd: rootPath }),
        execAsync('git status --porcelain', { cwd: rootPath }),
      ]);

      const gitInfo: GitInfo = {
        branch: branchResult.status === 'fulfilled' ? branchResult.value.stdout.trim() : 'unknown',
        commit: commitResult.status === 'fulfilled' ? commitResult.value.stdout.trim() : 'unknown',
        remote: remoteResult.status === 'fulfilled' ? remoteResult.value.stdout.trim() : undefined,
        status: statusResult.status === 'fulfilled' ? statusResult.value.stdout.trim().split('\n').filter(Boolean) : [],
      };

      return gitInfo;
    } catch {
      return undefined;
    }
  }

  private async getPackageInfo(rootPath: string): Promise<PackageInfo | undefined> {
    // Try different package managers
    const packageFiles = [
      { file: 'package.json', type: 'npm' as const },
      { file: 'requirements.txt', type: 'python' as const },
      { file: 'Cargo.toml', type: 'rust' as const },
      { file: 'go.mod', type: 'go' as const },
    ];

    for (const { file, type } of packageFiles) {
      try {
        const packagePath = path.join(rootPath, file);
        await fs.access(packagePath);

        if (type === 'npm') {
          return await this.parseNpmPackage(packagePath);
        } else if (type === 'python') {
          return await this.parsePythonPackage(packagePath, rootPath);
        } else if (type === 'rust') {
          return await this.parseRustPackage(packagePath);
        } else if (type === 'go') {
          return await this.parseGoPackage(packagePath);
        }
      } catch {
        // Continue to next package type
      }
    }

    return undefined;
  }

  private async parseNpmPackage(packagePath: string): Promise<PackageInfo> {
    const content = await fs.readFile(packagePath, 'utf8');
    const pkg = JSON.parse(content);

    return {
      name: pkg.name || 'unknown',
      version: pkg.version || '0.0.0',
      type: 'npm',
      dependencies: { ...pkg.dependencies, ...pkg.devDependencies },
      scripts: pkg.scripts || {},
    };
  }

  private async parsePythonPackage(packagePath: string, rootPath: string): Promise<PackageInfo> {
    const content = await fs.readFile(packagePath, 'utf8');
    const dependencies: Record<string, string> = {};

    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [name, version] = trimmed.split('==');
        dependencies[name || trimmed] = version || 'latest';
      }
    });

    // Try to get name from setup.py or pyproject.toml
    let name = 'python-project';
    try {
      const setupPath = path.join(rootPath, 'setup.py');
      const setupContent = await fs.readFile(setupPath, 'utf8');
      const nameMatch = setupContent.match(/name\s*=\s*['"]([^'"]+)['"]/);
      if (nameMatch) {
        name = nameMatch[1]!;
      }
    } catch {
      // Ignore setup.py read errors
    }

    return {
      name,
      version: '1.0.0',
      type: 'python',
      dependencies,
      scripts: {},
    };
  }

  private async parseRustPackage(packagePath: string): Promise<PackageInfo> {
    const content = await fs.readFile(packagePath, 'utf8');
    const dependencies: Record<string, string> = {};

    // Basic TOML parsing for dependencies
    const lines = content.split('\n');
    let inDependencies = false;

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed === '[dependencies]') {
        inDependencies = true;
        continue;
      }
      if (trimmed.startsWith('[') && trimmed !== '[dependencies]') {
        inDependencies = false;
        continue;
      }
      if (inDependencies && trimmed.includes('=')) {
        const [name, version] = trimmed.split('=').map(s => s.trim());
        dependencies[name!] = version!.replace(/['"]/g, '');
      }
    }

    // Extract name and version
    const nameMatch = content.match(/name\s*=\s*"([^"]+)"/);
    const versionMatch = content.match(/version\s*=\s*"([^"]+)"/);

    return {
      name: nameMatch?.[1] || 'rust-project',
      version: versionMatch?.[1] || '0.1.0',
      type: 'rust',
      dependencies,
      scripts: {},
    };
  }

  private async parseGoPackage(packagePath: string): Promise<PackageInfo> {
    const content = await fs.readFile(packagePath, 'utf8');
    const lines = content.split('\n');
    
    let name = 'go-project';
    const dependencies: Record<string, string> = {};

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('module ')) {
        name = trimmed.replace('module ', '');
      } else if (trimmed.includes(' v')) {
        const parts = trimmed.split(' ');
        if (parts.length >= 2) {
          dependencies[parts[0]!] = parts[1]!;
        }
      }
    }

    return {
      name,
      version: '1.0.0',
      type: 'go',
      dependencies,
      scripts: {},
    };
  }

  private isTextFile(filename: string): boolean {
    const textExtensions = new Set([
      '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx', '.py', '.rs', '.go',
      '.java', '.c', '.cpp', '.h', '.hpp', '.css', '.scss', '.html', '.xml',
      '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.sh', '.bat', '.ps1',
    ]);

    const ext = path.extname(filename).toLowerCase();
    return textExtensions.has(ext);
  }

  private getPermissions(mode: number): string {
    const permissions = [];
    
    // Owner permissions
    permissions.push((mode & 0o400) ? 'r' : '-');
    permissions.push((mode & 0o200) ? 'w' : '-');
    permissions.push((mode & 0o100) ? 'x' : '-');
    
    // Group permissions
    permissions.push((mode & 0o040) ? 'r' : '-');
    permissions.push((mode & 0o020) ? 'w' : '-');
    permissions.push((mode & 0o010) ? 'x' : '-');
    
    // Other permissions
    permissions.push((mode & 0o004) ? 'r' : '-');
    permissions.push((mode & 0o002) ? 'w' : '-');
    permissions.push((mode & 0o001) ? 'x' : '-');

    return permissions.join('');
  }
}
